from fastapi import APIRouter, Depends, HTTPException
from storage.models import StorageCategoriesResponse, StorageCategory, StorageInsertRequest, StoragePickupRequest
from domains.storage.service import storage_service
from infrastructure.repositories.storage_repository import StorageRepository

router = APIRouter()

@router.get("/categories", response_model=StorageCategoriesResponse)
async def get_storage_categories():
    """
    Returns a list of all storage categories.
    """
    repo = StorageRepository()
    categories_data = repo.get_all_categories()
    categories = [StorageCategory(**category) for category in categories_data]
    return StorageCategoriesResponse(success=True, categories=categories)

@router.post("/insert")
async def insert_storage(request: StorageInsertRequest):
    """
    Initiates the storage insertion process.
    """
    try:
        response = await storage_service.insert_storage(request.section_id)
        return response
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/pickup")
async def pickup_storage(request: StoragePickupRequest):
    """
    Initiates the storage pickup process.
    """
    try:
        response = await storage_service.pickup_storage(request.reservation_pin)
        return response
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
