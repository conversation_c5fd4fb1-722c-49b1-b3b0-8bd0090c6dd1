"""
Storage Flow Coordinator.
Orchestrates the entire storage flow by combining flow engine and step handlers.
"""

import logging
from typing import Dict, Any

from .flow_engine import FlowEngine
from .step_handlers import create_step_handler, PaymentHandler

logger = logging.getLogger(__name__)

class FlowCoordinator:
    """Coordinates flow execution and WebSocket communication"""
    
    def __init__(self):
        self.active_flows: Dict[str, FlowEngine] = {}
        self.active_handlers: Dict[str, Dict[str, Any]] = {}  # session_id -> {current_handler, step_type}
        
    async def start_flow(self, session_id: str, flow_config: Dict[str, Any]) -> bool:
        """Start a new flow for a session"""
        try:
            logger.info(f"Starting storage flow for session {session_id}")
            
            flow_engine = FlowEngine()
            flow_engine.build_flow(flow_config)
            
            self.active_flows[session_id] = flow_engine
            self.active_handlers[session_id] = {"current_handler": None, "step_type": None}
            
            logger.info(f"Storage flow created for session {session_id} with {len(flow_engine.steps)} steps")
            logger.info(f"Flow prepared for session {session_id}, waiting for WebSocket connection")
            return True
            
        except Exception as e:
            logger.error(f"Error starting storage flow for session {session_id}: {e}")
            return False
    
    async def execute_current_step(self, session_id: str) -> bool:
        """Execute the current step in the flow"""
        if session_id not in self.active_flows:
            logger.error(f"No active flow for session {session_id}")
            return False
            
        flow_engine = self.active_flows[session_id]
        current_step = flow_engine.get_current_step()
        
        if not current_step:
            logger.info(f"No more steps to execute for session {session_id}")
            return True
            
        try:
            logger.info(f"Preparing step '{current_step['type']}' for session {session_id}")
            
            handler = create_step_handler(current_step["type"], session_id)
            if not handler:
                logger.error(f"Failed to create handler for step type '{current_step['type']}'")
                return False
                
            self.active_handlers[session_id]["current_handler"] = handler
            self.active_handlers[session_id]["step_type"] = current_step["type"]
            
            logger.info(f"Step '{current_step['type']}' prepared for session {session_id}, waiting for WebSocket message loop")
            return True
                
        except Exception as e:
            logger.error(f"Error preparing step for session {session_id}: {e}")
            return False
    
    async def execute_current_step_async(self, session_id: str) -> bool:
        """Execute the current step in the flow asynchronously"""
        if session_id not in self.active_flows:
            logger.error(f"No active flow for session {session_id}")
            return False
            
        flow_engine = self.active_flows[session_id]
        current_step = flow_engine.get_current_step()
        
        if not current_step:
            logger.info(f"No more steps to execute for session {session_id}")
            return True
            
        try:
            logger.info(f"Executing step '{current_step['type']}' for session {session_id}")
            
            handler = create_step_handler(current_step["type"], session_id)
            if not handler:
                logger.error(f"Failed to create handler for step type '{current_step['type']}'")
                return False
                
            self.active_handlers[session_id]["current_handler"] = handler
            self.active_handlers[session_id]["step_type"] = current_step["type"]
            
            success = await handler.execute(current_step.get("context", {}))
            
            if success:
                logger.info(f"Step '{current_step['type']}' executed successfully for session {session_id}")
                
                if current_step["type"] == "payment":
                    logger.info(f"Payment step started, waiting for callback for session {session_id}")
                    return True
                else:
                    return await self.complete_current_step(session_id)
            else:
                logger.error(f"Step '{current_step['type']}' failed for session {session_id}")
                return False
                
        except Exception as e:
            logger.error(f"Error executing step for session {session_id}: {e}")
            return False
    
    async def complete_current_step(self, session_id: str) -> bool:
        """Complete current step and move to next"""
        if session_id not in self.active_flows:
            logger.error(f"No active flow for session {session_id}")
            return False
            
        flow_engine = self.active_flows[session_id]
        
        logger.info(f"Completing current step for session {session_id}")
        
        completed = flow_engine.complete_current_step()
        if not completed:
            logger.error(f"Failed to complete current step for session {session_id}")
            return False
        
        has_next = flow_engine.move_to_next_step()
        if has_next:
            next_step = flow_engine.get_current_step()
            logger.info(f"Moving to next step: {next_step['type']}")
            
            return await self.execute_current_step_async(session_id)
        else:
            logger.info(f"Flow completed for session {session_id}")
            await self.cleanup_flow(session_id)
            return True
    
    async def handle_websocket_message(self, session_id: str, message: Dict[str, Any]) -> bool:
        """Handle incoming WebSocket message from client"""
        logger.info(f"Handling WebSocket message for session {session_id}: {message.get('type', 'unknown')}")
        
        if session_id not in self.active_flows:
            logger.error(f"No active flow for session {session_id}")
            return False
            
        message_type = message.get("type")
        
        if message_type and message_type.endswith("_screen_ready"):
            logger.info(f"Received screen ready signal: {message_type} for session {session_id}")
            
            screen_type = message_type.replace("_screen_ready", "")
            
            handler_info = self.active_handlers.get(session_id, {})
            current_handler = handler_info.get("current_handler")
            
            if current_handler and hasattr(current_handler, 'handle_screen_ready'):
                try:
                    success = current_handler.handle_screen_ready(screen_type)
                    if success:
                        logger.info(f"Successfully handled {message_type} for session {session_id}")
                        return True
                    else:
                        logger.warning(f"Handler rejected {message_type} for session {session_id}")
                        return True
                except Exception as e:
                    logger.error(f"Error handling screen ready signal in step handler: {e}")
                    return False
            else:
                logger.warning(f"No handler or handler doesn't support screen ready for {message_type} in session {session_id}")
                return False
        
        handler_info = self.active_handlers.get(session_id, {})
        current_handler = handler_info.get("current_handler")
        
        if current_handler and hasattr(current_handler, 'handle_message'):
            try:
                return await current_handler.handle_message(message)
            except Exception as e:
                logger.error(f"Error handling message in step handler: {e}")
                return False
        
        logger.warning(f"No handler for message type '{message_type}' in session {session_id}")
        return False
    
    async def handle_payment_callback(self, session_id: str, status: str, message: str = "") -> bool:
        """Handle payment callback from payment service"""
        logger.info(f"Handling payment callback for session {session_id}: {status}")
        
        if session_id not in self.active_flows:
            logger.error(f"No active flow for session {session_id}")
            return False
            
        handler_info = self.active_handlers.get(session_id, {})
        current_handler = handler_info.get("current_handler")
        step_type = handler_info.get("step_type")
        
        if step_type != "payment" or not isinstance(current_handler, PaymentHandler):
            logger.error(f"Payment callback received but current step is not payment for session {session_id}")
            return False
        
        try:
            await current_handler.handle_payment_callback(status, message)
            
            if current_handler.is_completed():
                if current_handler.is_successful():
                    logger.info(f"Payment successful, moving to next step for session {session_id}")
                    return await self.complete_current_step(session_id)
                else:
                    logger.error(f"Payment failed for session {session_id}")
                    await self.cleanup_flow(session_id)
                    return True
            else:
                logger.info(f"Payment still processing for session {session_id}")
                return True
                
        except Exception as e:
            logger.error(f"Error handling payment callback for session {session_id}: {e}")
            return False
    
    async def cleanup_flow(self, session_id: str):
        """Clean up flow resources"""
        logger.info(f"Cleaning up flow for session {session_id}")
        
        if session_id in self.active_flows:
            del self.active_flows[session_id]
            
        if session_id in self.active_handlers:
            del self.active_handlers[session_id]

# Global instance
flow_coordinator = FlowCoordinator()
