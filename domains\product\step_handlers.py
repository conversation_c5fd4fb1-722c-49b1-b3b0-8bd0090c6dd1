"""
Step Handlers for Product Flow.
Each handler processes specific step type (age_verification, payment, hardware).
"""

import logging
import asyncio
from typing import Dict, Any, Optional
from abc import ABC, abstractmethod
import httpx

# Fix the import - use the instance, not the module
from managers.sequence_manager import sequence_manager
from managers.ws_manager import ws_manager
from config import device_config


logger = logging.getLogger(__name__)

class StepHandler(ABC):
    """Base class for flow step handlers"""
    
    def __init__(self, session_id: str):
        self.session_id = session_id
        self.websocket_connected = False
        self.waiting_for_ready = False
        self.ready_event = asyncio.Event()
        
    async def check_websocket_connection(self) -> bool:
        """Check if WebSocket is connected for this session"""
        self.websocket_connected = ws_manager.is_connected(self.session_id)
        if not self.websocket_connected:
            logger.warning(f"WebSocket not connected for session {self.session_id}")
        else:
            logger.info(f"WebSocket connected for session {self.session_id}")
        return self.websocket_connected
    
    async def send_websocket_message(self, message: Dict[str, Any]) -> bool:
        """Send JSON message via WebSocket"""
        if not await self.check_websocket_connection():
            return False
            
        try:
            await ws_manager.send(self.session_id, message)
            logger.info(f"Sent WebSocket message to {self.session_id}: {message}")
            return True
        except Exception as e:
            logger.error(f"Failed to send WebSocket message to {self.session_id}: {e}")
            return False
    
    async def wait_for_websocket_ready(self, screen_type: str, timeout: int = 60) -> bool:
        """Wait for Flutter to signal screen readiness"""
        logger.info(f"Waiting for {screen_type}_screen_ready from session {self.session_id}")
        
        # Reset ready event
        self.ready_event.clear()
        self.waiting_for_ready = True
        
        # Send start screen message with wait_for_ready flag
        show_message = {
            "type": f"start_{screen_type}_screen",
            "wait_for_ready": True
        }
        
        if not await self.send_websocket_message(show_message):
            self.waiting_for_ready = False
            return False
        
        try:
            # Wait for ready signal with timeout
            await asyncio.wait_for(self.ready_event.wait(), timeout=timeout)
            logger.info(f"Received {screen_type}_screen_ready for session {self.session_id}")
            return True
        except asyncio.TimeoutError:
            logger.error(f"Timeout waiting for {screen_type}_screen_ready from session {self.session_id} after {timeout}s")
            return False
        finally:
            self.waiting_for_ready = False
    
    def handle_screen_ready(self, screen_type: str) -> bool:
        """Handle screen ready signal from HMI"""
        if self.waiting_for_ready:
            logger.info(f"Received {screen_type}_screen_ready for session {self.session_id}")
            self.ready_event.set()
            return True
        else:
            logger.warning(f"Received {screen_type}_screen_ready but not waiting for it in session {self.session_id} (likely received after timeout)")
            return False
    
    @abstractmethod
    async def execute(self, context: Dict[str, Any]) -> bool:
        """Execute the step"""
        pass

class AgeVerificationHandler(StepHandler):
    """Handler for age verification step"""
    
    async def execute(self, context: Dict[str, Any]) -> bool:
        """Execute age verification step"""
        logger.info(f"Starting age verification for session {self.session_id}")
        
        try:
            # Wait for Flutter to show age verification screen
            if not await self.wait_for_websocket_ready("age_verification"):
                logger.error(f"Failed to wait for age verification screen readiness for session {self.session_id}")
                return False
            
            # Send processing status
            await self.send_websocket_message({
                "type": "age_verification_status",
                "status": "processing"
            })
            
            # Simulate age verification process
            await asyncio.sleep(2)
            
            # Send verification result
            await self.send_websocket_message({
                "type": "age_verification_result", 
                "success": True,
                "message": "Věková kontrola proběhla úspěšně"
            })
            
            logger.info(f"Age verification completed for session {self.session_id}")
            return True
            
        except Exception as e:
            logger.error(f"Age verification error for session {self.session_id}: {e}")
            await self.send_websocket_message({
                "type": "age_verification_result",
                "success": False, 
                "error": str(e),
                "message": "Chyba při věkové kontrole"
            })
            return False

class PaymentHandler(StepHandler):
    """Handler for payment step"""
    
    def __init__(self, session_id: str):
        super().__init__(session_id)
        self.payment_started = False
        self.payment_completed = False
        self.payment_success = False
        self.section_id = None
        self.payment_timeout_task = None
    
    async def execute(self, context: Dict[str, Any]) -> bool:
        """Execute payment step"""
        logger.info(f"Starting payment for session {self.session_id}")
        
        try:
            # Wait for Flutter to show payment screen
            if not await self.wait_for_websocket_ready("payment"):
                logger.error(f"Failed to wait for payment screen readiness for session {self.session_id}")
                return False
            
            # Send payment initiation message
            await self.send_websocket_message({
                "type": "payment_status",
                "status": "initiating",
                "message": "Zahajuji platbu..."
            })
            
            # Start payment processing
            amount = context.get('amount', 0)
            section_id = context.get('section_id')
            
            # Store section_id for payment callback
            self.section_id = section_id
            
            payment_data = {
                "type": "sale",
                "amount": amount,
                "variable_symbol": self.session_id  # Use session_id instead of section_id for unique identification
            }
            
            logger.info(f"Starting payment processing: {payment_data}")
            
            # Send payment request to payment service
            try:
                payment_service_timeout = device_config.payment_config["payment_service_timeout"]
                payment_service_url = device_config.payment_config["payment_service_url"]
                
                async with httpx.AsyncClient(timeout=payment_service_timeout) as client:
                    response = await client.post(
                        payment_service_url, 
                        json=payment_data
                    )
                    
                    if response.status_code == 200:
                        logger.info(f"Payment service response: {response.status_code} - {response.text}")
                        self.payment_started = True
                        
                        # Send payment processing status
                        await self.send_websocket_message({
                            "type": "payment_status",
                            "status": "processing",
                            "message": "Platba se zpracovává..."
                        })
                        
                        # Start payment timeout task
                        payment_timeout = device_config.payment_config["payment_timeout"]
                        self.payment_timeout_task = asyncio.create_task(self._payment_timeout_handler(payment_timeout))
                        
                        # Wait for payment completion (handled by callback)
                        # The flow will continue when payment_success is called
                        return True
                        
                    else:
                        logger.error(f"Payment service error: {response.status_code} - {response.text}")
                        await self.send_websocket_message({
                            "type": "payment_status",
                            "status": "error",
                            "message": f"Chyba platební služby: {response.status_code}"
                        })
                        return False
                        
            except Exception as e:
                logger.error(f"Payment service connection error: {e}")
                await self.send_websocket_message({
                    "type": "payment_status", 
                    "status": "error",
                    "message": f"Chyba při připojení k platební službě: {str(e)}"
                })
                return False
                
        except Exception as e:
            logger.error(f"Payment error for session {self.session_id}: {e}")
            await self.send_websocket_message({
                "type": "payment_status",
                "status": "error",
                "error": str(e),
                "message": "Chyba při zpracování platby"
            })
            return False
    
    async def _payment_timeout_handler(self, timeout_seconds: int):
        """Handle payment timeout after specified seconds"""
        try:
            # Wait for specified timeout
            logger.info(f"Starting payment timeout wait for {timeout_seconds}s for session {self.session_id}")
            await asyncio.sleep(timeout_seconds)
            
            # Check if payment is still not completed
            logger.info(f"Payment timeout wait completed for session {self.session_id}, payment_completed: {self.payment_completed}")
            if not self.payment_completed:
                logger.warning(f"Payment timeout after {timeout_seconds}s for session {self.session_id}")
                
                # Mark payment as failed due to timeout
                self.payment_success = False
                self.payment_completed = True
                
                # Send timeout message to websocket (same format as payment failure)
                logger.info(f"Sending payment timeout message to websocket for session {self.session_id}")
                success = await self.send_websocket_message({
                    "type": "payment_result",
                    "success": False,
                    "message": f"Platba vypršela - timeout {timeout_seconds}s"
                })
                logger.info(f"Payment timeout websocket message sent successfully: {success} for session {self.session_id}")
                
                # Notify flow coordinator about timeout
                try:
                    from domains.product.flow_coordinator import flow_coordinator
                    await flow_coordinator.handle_payment_timeout(self.session_id)
                except Exception as e:
                    logger.error(f"Error notifying flow coordinator about payment timeout: {e}")
                
                logger.info(f"Payment timeout handled for session {self.session_id}")
                
        except asyncio.CancelledError:
            # Task was cancelled (payment completed before timeout)
            logger.info(f"Payment timeout task cancelled for session {self.session_id} (payment completed or flow cleaned up)")
        except Exception as e:
            logger.error(f"Error in payment timeout handler for session {self.session_id}: {e}")

    async def handle_payment_callback(self, status: str, message: str = ""):
        """Handle payment callback from payment service"""
        logger.info(f"Payment callback for session {self.session_id}: {status}")
        
        # Cancel timeout task if it exists and payment is not already completed
        if not self.payment_completed and self.payment_timeout_task and not self.payment_timeout_task.done():
            self.payment_timeout_task.cancel()
            self.payment_timeout_task = None
            logger.info(f"Cancelled payment timeout task for session {self.session_id}")
        
        if status == "success":
            self.payment_success = True
            self.payment_completed = True
            
            # Update product status to 0 after successful payment
            try:
                from infrastructure.repositories import product_repository
                
                if self.section_id:
                    await product_repository.update_payment_status(int(self.section_id))
                    logger.info(f"Updated product status to 0 for section {self.section_id} after successful payment")
                else:
                    logger.warning(f"No section_id available to update product status for session {self.session_id}")
            except Exception as e:
                logger.error(f"Error updating product status after payment: {e}")
            
            await self.send_websocket_message({
                "type": "payment_result",
                "success": True,
                "message": "Platba byla úspěšná"
            })
            
        else:
            self.payment_success = False
            self.payment_completed = True
            
            await self.send_websocket_message({
                "type": "payment_result", 
                "success": False,
                "message": f"Platba neproběhla: {message}"
            })
    
    def is_completed(self) -> bool:
        """Check if payment is completed"""
        return self.payment_completed
    
    def is_successful(self) -> bool:
        """Check if payment was successful"""
        return self.payment_success

class HardwareHandler(StepHandler):
    """Handler for hardware operations step"""
    
    async def execute(self, context: Dict[str, Any]) -> bool:
        """Execute hardware step"""
        logger.info(f"Starting hardware operation for session {self.session_id}")
        
        try:
            section_id = context.get('section_id')
            if not section_id:
                logger.error(f"No section_id in context for session {self.session_id}")
                return False
            
            # Wait for Flutter to show hardware screen
            if not await self.wait_for_websocket_ready("hardware"):
                logger.error(f"Failed to wait for hardware screen readiness for session {self.session_id}")
                return False
            
            # Send hardware operation initiation
            await self.send_websocket_message({
                "type": "hardware_status",
                "status": "preparing",
                "message": "Připravuji schránku..."
            })
            
            logger.info(f"Starting hardware operation for session {self.session_id}, section: {section_id}")
            
            # Get hardware configuration for section
            from domains.product.service import product_service
            hardware_config = await product_service.get_hardware_config_for_section(int(section_id))
            
            if not hardware_config:
                logger.error(f"No hardware config found for section {section_id}")
                await self.send_websocket_message({
                    "type": "hardware_result",
                    "success": False,
                    "message": f"Hardware configuration not found for section {section_id}"
                })
                return False
            
            sections = [hardware_config]
            
            # Start FSM sequence
            success = await sequence_manager.start_fsm_sequence(
                session_id=self.session_id,
                sections=sections,
                pin="1234"  # Default pin
            )
            
            # Ignore failed states as requested - just log the attempt
            if success:
                logger.info(f"Hardware sequence started successfully for session {self.session_id}")
            else:
                logger.warning(f"Hardware sequence failed to start for session {self.session_id}, but continuing as requested")
            
            # Always return success since we want to ignore failed states
            logger.info(f"Hardware operation completed for session {self.session_id}")
            return True
                
        except Exception as e:
            logger.error(f"Hardware error for session {self.session_id}: {e}")
            await self.send_websocket_message({
                "type": "hardware_result",
                "success": False,
                "error": str(e),
                "message": "Chyba při ovládání schránky"
            })
            return False

# Factory function for creating step handlers
def create_step_handler(step_type: str, session_id: str) -> Optional[StepHandler]:
    """Create appropriate step handler based on step type"""
    handlers = {
        "age_verification": AgeVerificationHandler,
        "payment": PaymentHandler,
        "hardware": HardwareHandler
    }
    
    handler_class = handlers.get(step_type)
    if handler_class:
        return handler_class(session_id)
    
    logger.error(f"Unknown step type: {step_type}")
    return None 