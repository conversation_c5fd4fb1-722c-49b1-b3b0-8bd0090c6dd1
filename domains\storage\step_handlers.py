"""
Step Handlers for Storage Flow.
Each handler processes a specific step type (payment, hardware).
"""

import logging
import asyncio
from typing import Dict, Any, Optional
from abc import ABC, abstractmethod
import httpx

from managers.sequence_manager import sequence_manager
from managers.ws_manager import ws_manager
from config import device_config
from infrastructure.repositories.storage_repository import StorageRepository

logger = logging.getLogger(__name__)

class StepHandler(ABC):
    """Base class for flow step handlers"""
    
    def __init__(self, session_id: str):
        self.session_id = session_id
        self.websocket_connected = False
        self.waiting_for_ready = False
        self.ready_event = asyncio.Event()
        
    async def check_websocket_connection(self) -> bool:
        """Check if WebSocket is connected for this session"""
        self.websocket_connected = ws_manager.is_connected(self.session_id)
        if not self.websocket_connected:
            logger.warning(f"WebSocket not connected for session {self.session_id}")
        else:
            logger.info(f"WebSocket connected for session {self.session_id}")
        return self.websocket_connected
    
    async def send_websocket_message(self, message: Dict[str, Any]) -> bool:
        """Send JSON message via WebSocket"""
        if not await self.check_websocket_connection():
            return False
            
        try:
            await ws_manager.send(self.session_id, message)
            logger.info(f"Sent WebSocket message to {self.session_id}: {message}")
            return True
        except Exception as e:
            logger.error(f"Failed to send WebSocket message to {self.session_id}: {e}")
            return False
    
    async def wait_for_websocket_ready(self, screen_type: str, timeout: int = 60) -> bool:
        """Wait for the UI to signal screen readiness"""
        logger.info(f"Waiting for {screen_type}_screen_ready from session {self.session_id}")
        
        self.ready_event.clear()
        self.waiting_for_ready = True
        
        show_message = {
            "type": f"start_{screen_type}_screen",
            "wait_for_ready": True
        }
        
        if not await self.send_websocket_message(show_message):
            self.waiting_for_ready = False
            return False
        
        try:
            await asyncio.wait_for(self.ready_event.wait(), timeout=timeout)
            logger.info(f"Received {screen_type}_screen_ready for session {self.session_id}")
            return True
        except asyncio.TimeoutError:
            logger.error(f"Timeout waiting for {screen_type}_screen_ready from session {self.session_id} after {timeout}s")
            return False
        finally:
            self.waiting_for_ready = False
    
    def handle_screen_ready(self, screen_type: str) -> bool:
        """Handle screen ready signal from UI"""
        if self.waiting_for_ready:
            logger.info(f"Received {screen_type}_screen_ready for session {self.session_id}")
            self.ready_event.set()
            return True
        else:
            logger.warning(f"Received {screen_type}_screen_ready but not waiting for it in session {self.session_id}")
            return False
    
    @abstractmethod
    async def execute(self, context: Dict[str, Any]) -> bool:
        """Execute the step"""
        pass

class PaymentHandler(StepHandler):
    """Handler for payment step"""
    
    def __init__(self, session_id: str):
        super().__init__(session_id)
        self.payment_completed = False
        self.payment_success = False
        self.section_id = None
        self.size_category = None
        self.amount = None
        self.payment_timeout_task = None
    
    async def execute(self, context: Dict[str, Any]) -> bool:
        """Execute payment step"""
        logger.info(f"Starting payment for session {self.session_id}")
        
        try:
            if not await self.wait_for_websocket_ready("payment"):
                return False
            
            await self.send_websocket_message({"type": "payment_status", "status": "initiating"})
            
            self.amount = context.get('amount', 0)
            self.section_id = context.get('section_id')
            self.size_category = context.get('size_category')
            
            payment_data = {"type": "sale", "amount": self.amount, "variable_symbol": self.session_id}
            
            logger.info(f"Starting payment processing: {payment_data}")
            
            try:
                timeout = device_config.payment_config["payment_service_timeout"]
                url = device_config.payment_config["payment_service_url"]
                
                async with httpx.AsyncClient(timeout=timeout) as client:
                    response = await client.post(url, json=payment_data)
                    
                    if response.status_code == 200:
                        await self.send_websocket_message({"type": "payment_status", "status": "processing"})
                        timeout_val = device_config.payment_config["payment_timeout"]
                        self.payment_timeout_task = asyncio.create_task(self._payment_timeout_handler(timeout_val))
                        return True
                    else:
                        await self.send_websocket_message({"type": "payment_status", "status": "error", "message": f"Error: {response.status_code}"})
                        return False
                        
            except Exception as e:
                await self.send_websocket_message({"type": "payment_status", "status": "error", "message": str(e)})
                return False
                
        except Exception as e:
            logger.error(f"Payment error for session {self.session_id}: {e}")
            await self.send_websocket_message({"type": "payment_status", "status": "error", "error": str(e)})
            return False
    
    async def _payment_timeout_handler(self, timeout_seconds: int):
        try:
            await asyncio.sleep(timeout_seconds)
            if not self.payment_completed:
                self.payment_success = False
                self.payment_completed = True
                await self.send_websocket_message({"type": "payment_result", "success": False, "message": "Payment timed out"})
                from domains.storage.flow_coordinator import flow_coordinator
                await flow_coordinator.cleanup_flow(self.session_id)
        except asyncio.CancelledError:
            logger.info(f"Payment timeout task cancelled for session {self.session_id}")
        except Exception as e:
            logger.error(f"Error in payment timeout handler for session {self.session_id}: {e}")

    async def handle_payment_callback(self, status: str, message: str = ""):
        if not self.payment_completed and self.payment_timeout_task and not self.payment_timeout_task.done():
            self.payment_timeout_task.cancel()

        if status == "success":
            self.payment_success = True
            self.payment_completed = True
            
            repo = StorageRepository()
            from managers.session_manager import session_manager
            session = session_manager.get_session(self.session_id)
            operation = session.get("operation")

            try:
                if operation == "storage_insert":
                    pin = repo.create_reservation(self.section_id, self.amount, self.size_category)
                    logger.info(f"Created storage reservation for section {self.section_id}")
                    await self.send_websocket_message({"type": "payment_result", "success": True, "message": "Payment successful", "reservation_pin": pin})
                elif operation == "storage_pickup":
                    reservation_id = session.get("reservation_id")
                    repo.deactivate_reservation(reservation_id)
                    logger.info(f"Deactivated storage reservation {reservation_id}")
                    await self.send_websocket_message({"type": "payment_result", "success": True, "message": "Payment successful"})
            except Exception as e:
                logger.error(f"Error processing storage operation after payment: {e}")
                await self.send_websocket_message({"type": "payment_result", "success": False, "message": "Error processing operation"})
            
        else:
            self.payment_success = False
            self.payment_completed = True
            await self.send_websocket_message({"type": "payment_result", "success": False, "message": f"Payment failed: {message}"})
    
    def is_completed(self) -> bool:
        return self.payment_completed
    
    def is_successful(self) -> bool:
        return self.payment_success

class HardwareHandler(StepHandler):
    """Handler for hardware operations step"""
    
    async def execute(self, context: Dict[str, Any]) -> bool:
        logger.info(f"Starting hardware operation for session {self.session_id}")
        
        try:
            section_id = context.get('section_id')
            if not section_id:
                return False
            
            if not await self.wait_for_websocket_ready("hardware"):
                return False
            
            await self.send_websocket_message({"type": "hardware_status", "status": "preparing"})
            
            from domains.storage.service import storage_service
            # This needs to be implemented in storage_service
            hardware_config = await storage_service.get_hardware_config_for_section(int(section_id))
            
            if not hardware_config:
                await self.send_websocket_message({"type": "hardware_result", "success": False, "message": "Hardware config not found"})
                return False
            
            sections = [hardware_config]
            
            success = await sequence_manager.start_fsm_sequence(session_id=self.session_id, sections=sections, pin="0000") # Using a dummy PIN
            
            return True # Always return true as per requirement
                
        except Exception as e:
            logger.error(f"Hardware error for session {self.session_id}: {e}")
            await self.send_websocket_message({"type": "hardware_result", "success": False, "error": str(e)})
            return False

def create_step_handler(step_type: str, session_id: str) -> Optional[StepHandler]:
    """Create appropriate step handler based on step type"""
    handlers = {
        "payment": PaymentHandler,
        "hardware": HardwareHandler
    }
    handler_class = handlers.get(step_type)
    if handler_class:
        return handler_class(session_id)
    return None
