import mysql.connector
import json
from os import getenv
from typing import List, Dict, Optional

class SectionRepository:
    def list_sections(self) -> List[Dict]:
        conn = mysql.connector.connect(
            host=getenv("DB_HOST"),
            port=int(getenv("DB_PORT")),
            database=getenv("DB_NAME"),
            user=getenv("DB_USER"),
            password=getenv("DB_PASSWORD")
        )
        cursor = conn.cursor(dictionary=True)
        try:
            cursor.execute("""
                SELECT section_id, identification_name, tempered, blocked, service, lock_id, led_section, mode, type, width, length, height, size_category
                FROM box_sections
                WHERE visible = 1
                ORDER BY section_id ASC
            """)
            return [dict(row) for row in cursor.fetchall()]
        finally:
            cursor.close()
            
    def get_section_data(self, section_id: str) -> Dict:
        """Vrátí data pro konkrétní section_id včetně visible flag"""
        conn = mysql.connector.connect(
            host=getenv("DB_HOST"),
            port=int(getenv("DB_PORT")),
            database=getenv("DB_NAME"),
            user=getenv("DB_USER"),
            password=getenv("DB_PASSWORD")
        )
        cursor = conn.cursor(dictionary=True)
        try:
            cursor.execute("""
                SELECT section_id, identification_name, tempered, blocked, service, lock_id, led_section, mode, type, visible
                FROM box_sections
                WHERE section_id = %s
            """, (section_id,))
            result = cursor.fetchone()
            if result:
                return dict(result)
            else:
                # Section neexistuje - vrátíme default data
                return {
                    "section_id": section_id,
                    "identification_name": None,
                    "tempered": 0,
                    "blocked": 0,
                    "service": 0,
                    "lock_id": None,
                    "led_section": None,
                    "mode": None,
                    "type": None,
                    "visible": 0
                }
        finally:
            cursor.close()
            
    def get_box_layout(self) -> Optional[Dict]:
        """Vrátí layout z databáze podle posledního záznamu"""
        conn = mysql.connector.connect(
            host=getenv("DB_HOST"),
            port=int(getenv("DB_PORT")),
            database=getenv("DB_NAME"),
            user=getenv("DB_USER"),
            password=getenv("DB_PASSWORD")
        )
        cursor = conn.cursor(dictionary=True)
        try:
            cursor.execute("""
                SELECT box_layout
                FROM box_settings
                ORDER BY id DESC
                LIMIT 1
            """)
            result = cursor.fetchone()
            if result and result['box_layout']:
                return json.loads(result['box_layout'])
            return None
        finally:
            cursor.close()

section_repository = SectionRepository()
