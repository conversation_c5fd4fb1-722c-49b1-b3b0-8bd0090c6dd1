import mysql.connector
from os import getenv
from typing import List, Dict, Any

def get_db():
    return mysql.connector.connect(
        host=getenv("DB_HOST"),
        port=int(getenv("DB_PORT")),
        database=getenv("DB_NAME"),
        user=getenv("DB_USER"),
        password=getenv("DB_PASSWORD")
    )

class StorageRepository:
    def get_all_categories(self) -> List[Dict[str, Any]]:
        db = get_db()
        cursor = db.cursor(dictionary=True)
        cursor.execute("SELECT size_category, price FROM storage_categories")
        categories = cursor.fetchall()
        cursor.close()
        db.close()
        return categories

    def get_category_by_section(self, section_id: int) -> Dict[str, Any]:
        db = get_db()
        cursor = db.cursor(dictionary=True)
        query = """
            SELECT sc.size_category, sc.price
            FROM storage_categories sc
            JOIN box_sections bs ON sc.id = bs.size_category
            WHERE bs.section_id = %s
        """
        cursor.execute(query, (section_id,))
        category = cursor.fetchone()
        cursor.close()
        db.close()
        return category

    def create_reservation(self, section_id: int, price: float, size_category: int) -> str:
        db = get_db()
        cursor = db.cursor()
        pin = "1234" # Generate a random pin
        query = """
            INSERT INTO storage_reservations (section_id, price, category, status, reservation_pin)
            VALUES (%s, %s, %s, 1, %s)
        """
        cursor.execute(query, (section_id, price, size_category, pin))
        db.commit()
        cursor.close()
        db.close()
        return pin

    def find_reservation_by_pin(self, pin: str) -> Dict[str, Any]:
        db = get_db()
        cursor = db.cursor(dictionary=True)
        query = "SELECT * FROM storage_reservations WHERE reservation_pin = %s AND status = 1"
        cursor.execute(query, (pin,))
        reservation = cursor.fetchone()
        cursor.close()
        db.close()
        return reservation

    def deactivate_reservation(self, reservation_id: int):
        db = get_db()
        cursor = db.cursor()
        query = "UPDATE storage_reservations SET status = 0 WHERE id = %s"
        cursor.execute(query, (reservation_id,))
        db.commit()
        cursor.close()
        db.close()
